# runner: Github hosted being used - no tokens or secrets used
# This perform build checks before merging features into main branch 
name: StatusCheck
on:
 
 pull_request:
     branches:
      - 'main'

env:
  CONFIGURATION: Release
  DOTNET_CORE_VERSION: 6.0.x
  WORKING_DIRECTORY: WebAPI

jobs:
  statusbuild:
    runs-on: windows-latest
    steps:
       #- name: Branch Names
       #  id: get-current-branch
        # uses: tj-actions/branch-names@v6
       - name: Branch Names Updated
         run: |
           echo "Head ref : ${{github.head_ref}}"
           echo "Base ref : ${{github.base_ref}}"
       - name : Checkout Branch
         uses: actions/checkout@v3
         with:
         # ref: ${{ steps.get-current-branch.outputs.current_branch }}
          ref: ${{github.head_ref}}

       - name: Setup .NET 
         uses: actions/setup-dotnet@v3
         with:
          dotnet-version: ${{env.DOTNET_VERSION}} 

       - name: Restore dependencies
         run:  dotnet restore ${{env.WORKING_DIRECTORY}}  

       - name: Build
         run:  dotnet build "${{ env.WORKING_DIRECTORY }}" --configuration ${{ env.CONFIGURATION }} --no-restore
  
 
