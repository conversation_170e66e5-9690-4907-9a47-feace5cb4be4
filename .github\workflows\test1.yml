name: Testing Kudu API Deployment
on:
   workflow_dispatch:

        
jobs:
     
   test:
     runs-on:  [self-hosted,linux]
     environment: 'STAGE-SECONDARY'
     permissions:
      id-token: write
      contents: read
     steps:

          - name: Checkout
            uses: actions/checkout@v2
            with:
               ref: 'test'
          - name: IP Address
           # if: ${{ false }}
            run: |
              nslookup  app-cancerscreeningsms-tst02-secondary.scm.azurewebsites.net
            
          - name: Sign in to Azure
            uses: azure/login@v1
            with:
                client-id: ${{ vars.AZURE_CLIENT_ID }}
                tenant-id: ${{ vars.AZURE_TENANT_ID }}
                subscription-id: ${{ vars.AZURE_SUBSCRIPTION_ID }}


          - name: Kudu API Deployment
            # if: ${{ false }}  # working
            run: |
               $appname = "app-cancerscreeningsms-tst02-secondary"
               $username = '$app-cancerscreeningsms-tst02__secondary'
               $password = 'vLLQeQPurqngmnwc7L5iAYbioHQ2YB9ZpqYuqe2Tv7dur1qhZQSxsPYasA2f'
               $zipfile = "publish.zip"
               $apiUrl = "https://app-cancerscreeningsms-tst02-secondary.scm.azurewebsites.net/api/zip/site/wwwroot"
               $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("{0}:{1}" -f $username, $password)))
               $userAgent = "powershell/1.0"
               Invoke-RestMethod  -SkipCertificateCheck -Uri $apiUrl -Headers @{Authorization=("Basic {0}" -f $base64AuthInfo)} -UserAgent $userAgent -Method PUT -InFile $zipfile -ContentType "multipart/form-data" -ErrorAction Stop
            shell: pwsh  
         
         
          - name: Az CLI command
            if: ${{false}}
            run: |
               az webapp deploy --resource-group rg-tst-cancerscreeningapp-sms --name app-CancerScreeningSMS-TST02  --slot secondary  --src-path publish.zip
         
         # performs Zip Deployment
          - name: 'Deploying to Azure Web App'
            uses: azure/webapps-deploy@v2
            if: ${{false}}  # disable this step  
            with: 
              app-name: ${{vars.AZURE_WEBAPP_NAME}}   # variable get from specific environment
              slot-name: ${{vars.DEPLOYMENT_SLOT}}    # variable get from specific environment
              package: publish.zip
          
          - name: Logout with Azure CLI script
            uses: azure/CLI@v1
            with:
                inlineScript: |
                      az logout
                      az cache purge
                      az account clear   


