# PowerShell script to remove .idea folder from Git tracking and commit changes
# This script will:
# 1. Remove .idea folder from Git tracking (if it exists)
# 2. Add the updated .gitignore file
# 3. Commit the changes

Write-Host "Removing .idea folder from Git tracking..." -ForegroundColor Yellow

# Remove .idea folder from Git tracking (but keep it locally)
git rm -r --cached .idea/ 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ .idea folder removed from Git tracking" -ForegroundColor Green
} else {
    Write-Host "ℹ .idea folder was not tracked by Git (this is fine)" -ForegroundColor Cyan
}

# Add the updated .gitignore file
Write-Host "Adding updated .gitignore file..." -ForegroundColor Yellow
git add .gitignore

# Check if there are changes to commit
$status = git status --porcelain
if ($status) {
    # Commit the changes
    Write-Host "Committing changes..." -ForegroundColor Yellow
    git commit -m "feat: add .idea/ to .gitignore

- Added .idea/ directory to .gitignore to ignore JetBrains Rider IDE configuration files
- Removed .idea/ folder from Git tracking while keeping it locally
- This prevents IDE-specific settings from being committed to the repository"

    Write-Host "✓ Changes committed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Summary of changes:" -ForegroundColor Cyan
    Write-Host "- Added '.idea/' to .gitignore file" -ForegroundColor White
    Write-Host "- Removed .idea folder from Git tracking (if it was tracked)" -ForegroundColor White
    Write-Host "- The .idea folder will remain on your local machine but won't be tracked by Git" -ForegroundColor White
} else {
    Write-Host "ℹ No changes to commit" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "✓ .idea folder is now ignored by Git!" -ForegroundColor Green