#TODO: 
#Option 1: as public access to App service is blocked, we need to use self-hosted runner + (Private Endpt to scm endpoint?)
#Option 2: other option is to run command from GH action that enables public access, deploys and then disables
#     az account set --subscription PHCC-AzureEnterprise-Test
#     az resource update --resource-group RG-TST-CancerScreeningApp-SMS --name app-CancerScreeningSMS-TST02 --resource-type "Microsoft.Web/sites" --set properties.publicNetworkAccess=Enabled
#     ref: https://azure.github.io/AppService/2022/11/24/Advanced-access-restriction-scenarios-in-Azure-App-Service.html#first-advanced-scenario---filter-by-http-header
#     but we are facing an error - The client '<EMAIL>' with object id 'c4a1d27c-2ddc-4b5e-92ec-c423423d2eea' has permission to perform action 'Microsoft.Web/sites/write' on scope '/subscriptions/9481c82c-f3dd-4c67-b750-88a27a30d664/resourcegroups/RG-TST-CancerScreeningApp-SMS/providers/Microsoft.Web/sites/app-CancerScreeningSMS-TST02'; however, it does not have permission to perform action 'join/action' on the linked scope(s) '/subscriptions/9481c82c-f3dd-4c67-b750-88a27a30d664/resourceGroups/rg-webapi-prod-001/providers/Microsoft.Network/virtualNetworks/vnet-webapi-prod-001/subnets/snet-test-appservices' or the linked scope(s) are invalid.
#   solution - To allow the client to perform the 'join/action' action on the virtual network subnet, code can be deployed only through GH action, you can grant the client the 'Microsoft.Network/virtualNetworks/subnets/join/action' permission on the virtual network subnet resource, or grant the client the 'Microsoft.Network/virtualNetworks/subnets/joinViaResourceGroup/action' permission on the virtual network resource group 
#conclusion: wait for self hosted runner to be provisioned on Azure, then proceed with option 1

# publish profile deployment = giving a temporary password and scm and ftp urls to anyone to deploy (can be done from GH hosted runners but needsscm url to be public through AppGW; can also be done from self hosted runner once Pvt endpt  is cconfigured for scm url)
# workload identity or federated identity based deployment = an app registration is given access to resources, action runners (GH or self-hosted) would use this identity to access the resources.
# workload identity is better as the control is from App registration (each GH repo and each branch needs to be registerd as URI to get the access and can be revoked etc, only way to bypass is when someone takes control of GH code and branch and deploys from there), in case of publish profile theft, anyone can use it

Code: LinkedAuthorizationFailed
name: Staging Deployment
on:
#  push:
#    branches:
#    - main
  workflow_dispatch:

permissions:
  id-token: write
  contents: read

env:
  AZURE_WEBAPP_NAME: app-CancerScreeningSMS-TST02
  AZURE_WEBAPP_PACKAGE_PATH: WebAPI/published
  CONFIGURATION: Release
  DOTNET_CORE_VERSION: 6.0.x
  WORKING_DIRECTORY: WebAPI
jobs:
  build:
    runs-on:  ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - uses: microsoft/variable-substitution@v1 
      with:
        files: 'WebAPI/appsettings.json'
      env:
        Dynamics: ${{ secrets.DYNAMICS_STG }}
        
    - name: Setup .NET Core
      uses: actions/setup-dotnet@v1
      with:
        dotnet-version: ${{ env.DOTNET_CORE_VERSION }}
    - name: Restore
      run: dotnet restore "${{ env.WORKING_DIRECTORY }}"
    - name: Build
      run: dotnet build "${{ env.WORKING_DIRECTORY }}" --configuration ${{ env.CONFIGURATION }} --no-restore
    - name: Test
      run: dotnet test "${{ env.WORKING_DIRECTORY }}" --no-build
    - name: Publish
      run: dotnet publish "${{ env.WORKING_DIRECTORY }}" --configuration ${{ env.CONFIGURATION }} --no-build --output "${{ env.AZURE_WEBAPP_PACKAGE_PATH }}"
    - name: Publish Artifacts
      uses: actions/upload-artifact@v1.0.0
      with:
        name: webapp
        path: ${{ env.AZURE_WEBAPP_PACKAGE_PATH }}
    #- name: Deploy to Azure WebApp using publish profile
    #  uses: azure/webapps-deploy@v2
    #  with:
    #    app-name: ${{ env.AZURE_WEBAPP_NAME }}
    #    publish-profile: ${{ secrets.app_CancerScreeningSMS_TST02_FFFF }}
    #    package: ${{ env.AZURE_WEBAPP_PACKAGE_PATH }}
    
    # another mode of deployment
    - uses: azure/login@v1
      name: Sign in to Azure
      with:
        client-id: ${{ secrets.AZURE_CLIENT_ID }}
        tenant-id: ${{ secrets.AZURE_TENANT_ID }}
        subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
    - name: 'Run Azure webapp deploy action'
      uses: azure/webapps-deploy@v2
      with: 
        app-name: ${{ env.AZURE_WEBAPP_NAME }} 
        package: ${{ env.AZURE_WEBAPP_PACKAGE_PATH }}
    
   
    
   # - name: logout
   #   run: |
   #     az logout
   
   # if the runner is self-hosted which is not github provided it is recommended to manually logout at the end of the workflow
   #- name: Logout with Azure CLI script
   #  uses: azure/CLI@v1
   #  with:
   #    inlineScript: |
   #      az logout
   #      az cache purge
   #      az account clear