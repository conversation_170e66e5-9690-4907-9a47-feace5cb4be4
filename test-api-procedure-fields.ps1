# PowerShell script to test the enhanced Cancer Screening SMS API
# This script demonstrates how to test the new optional procedure fields

$baseUrl = "https://localhost:7000" # Update this to your actual API URL
$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer YOUR_TOKEN_HERE" # Update with actual token
}

Write-Host "Testing Cancer Screening SMS API with Optional Procedure Details" -ForegroundColor Cyan
Write-Host "=================================================================" -ForegroundColor Cyan

# Test 1: New appointment WITH procedure details
Write-Host "`n🧪 Test 1: New appointment WITH procedure details" -ForegroundColor Yellow

$testData1 = @{
    appointmentIdentifier = "TEST001"
    appointmentType = "Breast Screening"
    procedureName = "Screening Mammogram"
    procedureCode = "77067"
    appointmentLocation = "RAK"
    appointmentLocationDescription = "Rawdat Al Khail"
    appointmentDateTime = "2025-04-30T10:00:00"
    appointmentArriveMinutesBefore = 30
    customIdentifier = "HC00000001"
    subjectIdentifier = "HC00000001"
    recipientFirstName = "Test"
    recipientLastName = "Patient"
    recipientPhone = "12345678"
    recipientEmail = "<EMAIL>"
    sendDateTime = "2025-04-28T12:48:50"
    reminderSendHoursBefore = 24
    reminderType = "Screening"
    recipientNationality = "undefined"
} | ConvertTo-Json

Write-Host "Request Body:" -ForegroundColor Gray
Write-Host $testData1 -ForegroundColor Gray

try {
    $response1 = Invoke-RestMethod -Uri "$baseUrl/api/appointment/new" -Method POST -Body $testData1 -Headers $headers
    Write-Host "✅ SUCCESS: Appointment created with procedure details" -ForegroundColor Green
    Write-Host "Response: $($response1 | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: New appointment WITHOUT procedure details (backward compatibility)
Write-Host "`n🧪 Test 2: New appointment WITHOUT procedure details (backward compatibility)" -ForegroundColor Yellow

$testData2 = @{
    appointmentIdentifier = "TEST002"
    appointmentType = "Breast Screening"
    appointmentLocation = "RAK"
    appointmentLocationDescription = "Rawdat Al Khail"
    appointmentDateTime = "2025-04-30T11:00:00"
    appointmentArriveMinutesBefore = 30
    customIdentifier = "HC00000002"
    subjectIdentifier = "HC00000002"
    recipientFirstName = "Test"
    recipientLastName = "Patient2"
    recipientPhone = "12345679"
    recipientEmail = "<EMAIL>"
    sendDateTime = "2025-04-28T12:48:50"
    reminderSendHoursBefore = 24
    reminderType = "Screening"
    recipientNationality = "undefined"
} | ConvertTo-Json

Write-Host "Request Body:" -ForegroundColor Gray
Write-Host $testData2 -ForegroundColor Gray

try {
    $response2 = Invoke-RestMethod -Uri "$baseUrl/api/appointment/new" -Method POST -Body $testData2 -Headers $headers
    Write-Host "✅ SUCCESS: Appointment created without procedure details (backward compatible)" -ForegroundColor Green
    Write-Host "Response: $($response2 | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Validation error - procedure name too long
Write-Host "`n🧪 Test 3: Validation error - procedure name too long (>200 chars)" -ForegroundColor Yellow

$longProcedureName = "A" * 201 # 201 characters

$testData3 = @{
    appointmentIdentifier = "TEST003"
    appointmentType = "Breast Screening"
    procedureName = $longProcedureName
    procedureCode = "77067"
    appointmentLocation = "RAK"
    appointmentLocationDescription = "Rawdat Al Khail"
    appointmentDateTime = "2025-04-30T12:00:00"
    appointmentArriveMinutesBefore = 30
    customIdentifier = "HC00000003"
    subjectIdentifier = "HC00000003"
    recipientFirstName = "Test"
    recipientLastName = "Patient3"
    recipientPhone = "12345680"
    recipientEmail = "<EMAIL>"
    sendDateTime = "2025-04-28T12:48:50"
    reminderSendHoursBefore = 24
    reminderType = "Screening"
    recipientNationality = "undefined"
} | ConvertTo-Json

try {
    $response3 = Invoke-RestMethod -Uri "$baseUrl/api/appointment/new" -Method POST -Body $testData3 -Headers $headers
    Write-Host "❌ UNEXPECTED: Should have failed validation" -ForegroundColor Red
} catch {
    Write-Host "✅ EXPECTED: Validation error for procedure name too long" -ForegroundColor Green
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Test 4: Validation error - procedure code too long
Write-Host "`n🧪 Test 4: Validation error - procedure code too long (>50 chars)" -ForegroundColor Yellow

$longProcedureCode = "A" * 51 # 51 characters

$testData4 = @{
    appointmentIdentifier = "TEST004"
    appointmentType = "Breast Screening"
    procedureName = "Screening Mammogram"
    procedureCode = $longProcedureCode
    appointmentLocation = "RAK"
    appointmentLocationDescription = "Rawdat Al Khail"
    appointmentDateTime = "2025-04-30T13:00:00"
    appointmentArriveMinutesBefore = 30
    customIdentifier = "HC00000004"
    subjectIdentifier = "HC00000004"
    recipientFirstName = "Test"
    recipientLastName = "Patient4"
    recipientPhone = "12345681"
    recipientEmail = "<EMAIL>"
    sendDateTime = "2025-04-28T12:48:50"
    reminderSendHoursBefore = 24
    reminderType = "Screening"
    recipientNationality = "undefined"
} | ConvertTo-Json

try {
    $response4 = Invoke-RestMethod -Uri "$baseUrl/api/appointment/new" -Method POST -Body $testData4 -Headers $headers
    Write-Host "❌ UNEXPECTED: Should have failed validation" -ForegroundColor Red
} catch {
    Write-Host "✅ EXPECTED: Validation error for procedure code too long" -ForegroundColor Green
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n📋 Test Summary:" -ForegroundColor Cyan
Write-Host "================" -ForegroundColor Cyan
Write-Host "✅ Test 1: New appointment with procedure details" -ForegroundColor Green
Write-Host "✅ Test 2: New appointment without procedure details (backward compatibility)" -ForegroundColor Green
Write-Host "✅ Test 3: Validation error for procedure name too long" -ForegroundColor Green
Write-Host "✅ Test 4: Validation error for procedure code too long" -ForegroundColor Green

Write-Host "`n📝 Notes:" -ForegroundColor Cyan
Write-Host "- Update the `$baseUrl` variable with your actual API URL" -ForegroundColor White
Write-Host "- Update the Authorization header with a valid token" -ForegroundColor White
Write-Host "- Ensure the API is running before executing these tests" -ForegroundColor White