#main workflow for Cancer Screening SMS API
#Github PAT token used for pushing version and changelogs back to main branch and Identity tied with PAT token has admin priviledges-  
# in future we should avoid using admin Identity PAT's in workflows. instead PAT should be created with repository write access role.
# This feature is requested in GitHub support forum for allowing specific user for bypass branch protection rules without admin privilege.  

name: Cancer Screening CI/CD Pipeline
run-name: Release Main Version -  ${{ github.run_number }} by @${{ github.actor }}
on:

 push:
   branches:
    - 'main'
   paths-ignore:
        - '.github/**'
        - 'README.md'
        - 'CODEOWNERS'
        - '.gitignore'
 workflow_dispatch:   # remove this trigger in future

jobs:

  call_create_tag:
    name: 'Create Release Tag'
    uses:  public-health-care-center-CORP/WorkflowRepo/.github/workflows/create-tag.yml@main
    with: 
       job-name: 'Create Release Tag'
       runner-name: 'linux'
       tag-name: 'release-1.0.${{ github.run_number }}'  # versioning: Major.Minor.BuildNumber # update here for changing version numbers.
       branch-name: main

   # running sonar qube code analysis on main branch    
  call_code_analysis:
    if: ${{false}}  # Disabling this job, Not working as expected. need to check later.
    name: 'Code Analysis'
    uses: public-health-care-center-CORP/CancerScreeningSMS/.github/workflows/sonarqube-analysis.yml@main
    needs: call_create_tag


  UpdateChangeLog_Version:
               name: 'Update ChangeLog and Version'
               runs-on: [self-hosted,linux] #self-hosted runner is used for this workflow
               needs: call_create_tag
               steps:
                       - name : Checkout Branch
                         uses: actions/checkout@v3   
                         with:
                            fetch-depth: 0
                            ref: version    
                       - name: Update Version Number
                         run: |
                            Set-Content -Path version.txt -Value ${{needs.call_create_tag.outputs.tag-name}}
                            echo "build version successfully updated"
                         shell: pwsh

                       - uses: mnadher/gh-get-current-pr@master
                         id: PR
                       - run: echo "Your PR number is ${{ steps.PR.outputs.number }} and its JSON is ${{ steps.PR.outputs.pr }}"
            
                       - name: update changelogs file
                         id: call-update-changelogs
                         uses: ./.github/actions/update-changelogs # this action should be placed in the same branch where the workflow is running - composite actions using relative path of checkout directory 
                         with:  
                               version-number: ${{needs.call_create_tag.outputs.tag-name}}
                               pr-number: ${{ steps.PR.outputs.number }}
      
                       - name: Commit Version and Release logs
                         run: |
                              git add version.txt
                              git add RELEASE.md
                              git config --global user.name "mnadher"
                              git config --global user.email "<EMAIL>"
                              git commit -m "version and RELEASE log updated"
                              git push


  build-main:

    runs-on: [self-hosted,linux] #self-hosted runner is used for this workflow
    needs: call_create_tag
    steps:
        
       - name : Checkout Branch
         uses: actions/checkout@v3   
         with:
          fetch-depth: 0
          ref: ${{needs.call_create_tag.outputs.tag-name}}
     #-----------------------code for version , change logs update

          # enable below step if running on github runner  
       # - name: Setup .NET
        # uses: actions/setup-dotnet@v3
        # with:
        #  dotnet-version: ${{vars.BUILD_DOTNET_CORE_VERSION}} 

       - name: Restore dependencies
         run:  dotnet restore ${{vars.BUILD_WORKING_DIRECTORY}}  
  
       - name: Build
         run:  dotnet build "${{ vars.BUILD_WORKING_DIRECTORY }}" --configuration ${{ vars.BUILD_CONFIGURATION }} --no-restore
      # uncomment coverity scan when credentials are ready
     # - name: Coverity Scan
      #   uses: synopsys-sig-community/synopsys-github-templates/coverity@v0.0.2
       #  with:
        #  coverity-url: ${{ secrets.COVERITY_URL }}
         # coverity-user: ${{ secrets.COVERITY_USER }}
          # coverity-passphrase: ${{ secrets.COVERITY_PASSPHRASE }}
          #coverity-project-name: ${{ github.event.repository.name }} # no need to set from our end
          #coverity-checker-options: --webapp-security
          #create-stream-and-project: true
          #github-token: ${{ secrets.GITHUB_TOKEN }} #no need to set from our end
  
       - name: Publish
         run:  dotnet publish "${{ vars.BUILD_WORKING_DIRECTORY }}" --configuration ${{ vars.BUILD_CONFIGURATION }} --no-build --output "${{ vars.BUILD_WEBAPP_PACKAGE_PATH }}"
  
       - name: Upload Artifact
         uses: actions/upload-artifact@v3.1.2
         with:
          name: ${{vars.BUILD_ARTIFACT_NAME}}
          path: ${{ vars.BUILD_WEBAPP_PACKAGE_PATH }} 
          if-no-files-found: error

       - name: Print Job Summary
         run:  echo "#### main branch build has been successfully completed!"  >>  $GITHUB_STEP_SUMMARY
          
      # - name: Display Artifact URL
       #  run: |
        #      gh api repos/public-health-care-center-CORP/CancerScreeningSMS/actions/runs/${{github.run_id}}/artifacts
       #  env:
       #   GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
# ---------------------------------------------------End of Build job----------------------------------     
  stage:
    name: 'Stage Workflow'
    needs: build-main
    uses: public-health-care-center-CORP/CancerScreeningSMS/.github/workflows/app-CancerScreeningSMS-stage.yml@main
    secrets: inherit  #passing repo secrets
  production:
     name: 'Production Workflow'
     needs: stage
     uses: public-health-care-center-CORP/CancerScreeningSMS/.github/workflows/app-CancerScreeningSMS-PRD.yml@main
     secrets: inherit #passing repo secrets
         
           
     
