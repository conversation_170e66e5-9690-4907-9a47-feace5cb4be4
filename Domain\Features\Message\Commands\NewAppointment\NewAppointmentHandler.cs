﻿using Core.Constants;
using Core.Dynamics;
using Core.Exceptions;
using FluentValidation;
using MediatR;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System.Text;

namespace Domain.Features.Message.Commands.NewAppointment
{
    public class NewAppointmentHandler : IRequestHandler<NewAppointmentRequest, NewAppointmentResponse>
    {
        private readonly ServiceClient _service;
        private readonly IValidator<NewAppointmentRequest> _validator = new NewAppointmentRequestValidator();
        private readonly MessageManager _messageManager;
        public NewAppointmentHandler(ServiceClient service, MessageManager messageManager)
        {
            _service = service;
            _messageManager = messageManager;
        }

        public async Task<NewAppointmentResponse> Handle(NewAppointmentRequest request, CancellationToken cancellationToken)
        {
            _validator.ValidateAndThrow(request);
            if (GetActiveMessages(request.CustomIdentifier).Result.Count > 0)
            {
                throw new BusinessException("There is also an active upcoming appointment for the patient. You can only reschedule or cancel this meeting.");
            }
            if (IsExistAppointmentMessage(request.AppointmentIdentifier))
            {
                throw new BusinessException("There is already a record for this appointment identitifer.");
            }
            var id = await CreateMessage(request, MessageType.NewAppointment);
            if (_messageManager.IsAppointmentReminder(request.AppointmentDateTime, request.ReminderSendHoursBefore))
            {
                await CreateMessage(request, MessageType.AppointmentReminder);
            }
            var message = _service.Retrieve("phcc_message", id, new ColumnSet("phcc_messageid"));

            var returnValue = new NewAppointmentResponse()
            {
                Id = id,
                MessageId = message.GetString("phcc_messageid")
            };

            return returnValue;
        }

        private async Task<Guid> CreateMessage(NewAppointmentRequest request, MessageType messageType)
        {
            var entity = new Entity("phcc_message");

            Entity? patient = _messageManager.GetPatientByHealthCardNumber(request.CustomIdentifier);

            Entity? location = _messageManager.GetLocationByCode(request.AppointmentLocation);

            if (location == null)
            {
                throw new BusinessException("Health centre does not exist on the lookup table for Cancer Screening SMS System.");
            }

            if (patient != null)
            {
                entity["regardingobjectid"] = new EntityReference(patient.LogicalName, patient.Id);
            }
            string language = _messageManager.NationalityCheck(request.RecipientNationality);
            entity["phcc_id"] = request.AppointmentIdentifier;
            entity["phcc_appointmenttype"] = request.AppointmentType;
            entity["phcc_locationid"] = new EntityReference(location.LogicalName, location.Id);
            entity["phcc_locationdescription"] = language == "English" ? location.GetString("msemr_text") : location.GetString("phcc_textarabic");
            entity["phcc_appointmentdatetime"] = request.AppointmentDateTime;
            entity["phcc_arriveminutesbefore"] = request.AppointmentArriveMinutesBefore;
            entity["phcc_recipientfirstname"] = request.RecipientFirstName;
            entity["phcc_recipientlastname"] = request.RecipientLastName;
            entity["phcc_recipientphone"] = request.RecipientPhone;
            entity["phcc_emailaddress"] = request.RecipientEmail;
            entity["phcc_scheduleddate"] = messageType == MessageType.AppointmentReminder ? request.AppointmentDateTime.AddHours(-request.ReminderSendHoursBefore) : request.SendDateTime;
            entity["phcc_reminderhoursbefore"] = request.ReminderSendHoursBefore;
            entity["phcc_recipientnationality"] = language;
            entity["phcc_recipienthealthcardnumber"] = request.CustomIdentifier;
            entity["statecode"] = new OptionSetValue((int)ActivityStatus.Scheduled);
            entity["statuscode"] = new OptionSetValue((int)MessageStatus.WaitingSend);
            entity["phcc_messagetype"] = new OptionSetValue((int)messageType);

            var id = await _service.CreateAsync(entity);

            return id;
        }

        private async Task<List<Entity>> GetActiveMessages(string customIdentifier)
        {
            QueryExpression queryExpression = new QueryExpression();
            queryExpression.EntityName = "phcc_message";
            queryExpression.NoLock = true;
            queryExpression.Criteria.AddCondition("phcc_appointmentdatetime", ConditionOperator.GreaterThan, TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("Arab Standard Time")));
            queryExpression.Criteria.AddCondition("phcc_messagetype", ConditionOperator.In, (int)MessageType.NewAppointment, (int)MessageType.RescheduleAppointment);
            queryExpression.Criteria.AddCondition("statuscode", ConditionOperator.NotEqual, (int)MessageStatus.Cancelled);
            queryExpression.Criteria.AddCondition("phcc_recipienthealthcardnumber", ConditionOperator.Equal, customIdentifier);
            EntityCollection collection = await _service.RetrieveMultipleAsync(queryExpression);
            return collection.Entities.ToList();
        }

        private bool IsExistAppointmentMessage(string appointmenIdentifier)
        {
            QueryExpression queryExpression = new QueryExpression("phcc_message");
            queryExpression.NoLock = true;
            queryExpression.Criteria.AddCondition("phcc_id", ConditionOperator.Equal, appointmenIdentifier);
            if (_service.RetrieveMultipleAsync(queryExpression).Result.Entities.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
