 param ([string]$branchName)
 $DebugPreference = 'Continue'  # Set debug message visible to console $DebugPreference = 'Continue'  # Set debug message visible to console
 $subscriptionId='9481c82c-f3dd-4c67-b750-88a27a30d664'
 $resourceGroupName='RG-TST-CancerScreeningApp-SMS'
 $appServiceName='ASP-CancerScreeningApp-SMS-TST'
 $slotName=$branchName.replace('/','')
 $appServicePlanName='app-CancerScreeningSMS-TST02'
 Write-Debug ('Subscription Id:'+$subscriptionId)
 Write-Debug ('resource Group Name:'+$resourceGroupName)
 Write-Debug ('app service name:'+$appServiceName)
 Write-Debug ('slot name:'+$slotName)

try {

 $response = Invoke-AzRestMethod -Method GET -Path "/subscriptions/$subscriptionId/resourceGroups/$resourceGroupName/providers/Microsoft.Web/sites/$appServiceName/slots/$slotName/?api-version=2022-03-01"
  Write-Debug ("Response Code:"+$response.StatusCode);
    
    if($response.StatusCode -eq '404')
    {
     Write-Host ( $slotName+" is not available.Lets create new slot");
   $isNewSlotCreated =   New-AzWebAppSlot -ResourceGroupName $resourceGroupName -Name $appServiceName -AppServicePlan $appServicePlanName -Slot $slotName
   
     if($isNewSlotCreated)
     {
      Write-Host ("Slot has been created successfully")
      exit 0

     }
     else
     {
      Write-Host ("There is an error while creatimg a slot")
      exit 1
     }

    }
    # Process response
}
catch {
    Write-Error "Error: $($_.Exception.Message)"
    # Handle error
    exit 1
}
