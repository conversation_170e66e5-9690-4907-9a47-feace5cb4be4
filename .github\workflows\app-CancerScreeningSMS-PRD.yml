name: Production Deployment
on:
  workflow_call: 
jobs:
  #production secondary slot deployment
  ProductionSecondaryDeployment:
    name: 'Production Secondary'
    permissions:
       id-token: write
       contents: read
    runs-on:  [self-hosted,linux]
    outputs:
     website: ${{ steps.get-website.outputs.website }} 
    environment:
       name: 'PRODUCTION-SECONDARY'  
   # if: ${{ false }} #disbaled - uncomment when go for Azure deployment 
    steps:
       - name: Download artifact from build job
         uses: actions/download-artifact@v3
         with:
           name: ${{vars.BUILD_ARTIFACT_NAME}}   # place artifact name which was built by previous job
           path: ${{vars.BUILD_DOWNLOAD_PATH}}
 
       - name: Display appSettings content
         run: |
             cat ${{vars.BUILD_DOWNLOAD_PATH}}/${{vars.BUILD_APPSETTING_NAME}}
             
     # - name: Zip Updated Build
        # run: Compress-Archive -Path "*" stagewebapp.zip
        # shell: pwsh
     # uncomment below steps when go for Azure deployment
       - name: Sign in to Azure
         uses: azure/login@v1
       
         with:
           client-id: ${{ vars.AZURE_CLIENT_ID }}
           tenant-id: ${{ vars.AZURE_TENANT_ID }}
           subscription-id: ${{ vars.AZURE_SUBSCRIPTION_ID }}
       - name: 'Deploying to Azure Web App'
         uses: azure/webapps-deploy@v2
         with: 
           app-name: ${{vars.AZURE_WEBAPP_NAME}}   # variable get from specific environment
           slot-name: ${{vars.DEPLOYMENT_SLOT}}    # variable get from specific environment
           package: ${{ vars.BUILD_DOWNLOAD_PATH }}  # repo variable
           
     #  - name:  Getting hostname
      #   id: generate-host-name
       #  run: |
        #      echo "websitename=$(az webapp config hostname list --resource-group  ${{vars.RESOURCE_GROUP_NAME}}  --webapp-name ${{vars.AZURE_WEBAPP_NAME}}  --slot ${{vars.DEPLOYMENT_SLOT}}   --query "[].name" --output tsv)" >> "$GITHUB_OUTPUT" 
      
       - name: Get Website
         id: get-website  
         run: |
             echo "website=${{vars.WEBSITE}}" >> "$GITHUB_OUTPUT"
 
       - name: Logout with Azure CLI script
         uses: azure/CLI@v1
         with:
             inlineScript: |
              az logout
              az cache purge
              az account clear  

  # production secondary slot api regression test            
  call-regression-production-seconadry-workflow:
    name: 'Secondary Slot Regression Test'
    needs: ProductionSecondaryDeployment
    uses: public-health-care-center-CORP/CancerScreeningSMS/.github/workflows/regression-api-test.yml@main
    secrets: inherit   #passing environment secrets
    with:
      collection_path1: ./api/Collection/*.postman_collection.json       
      report-path: 'Production-Secondary'
      host-name: ${{needs.ProductionSecondaryDeployment.outputs.website}}
      suppress-exit-code: --bail  # --bail : stops runner when test case fails ;--suppress-exit-code for suppress
      data-path: ./api/Data/data-production-secondary.json
      number-of-iteration: 1           
   


# Perform Prodution Slot swap
  call-slot-swap-prod-workflow:
    name: 'Production Slot Swap'
    needs: call-regression-production-seconadry-workflow
    uses: public-health-care-center-CORP/CancerScreeningSMS/.github/workflows/slot-swap.yml@main
    secrets: inherit   #passing environment secrets
    with:
      environment-e: 'PRODUCTION'       
     
