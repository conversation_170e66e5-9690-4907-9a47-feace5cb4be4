﻿using FluentValidation;

namespace Domain.Features.Message.Commands.NewAppointment
{
    public class NewAppointmentRequestValidator : AbstractValidator<NewAppointmentRequest>
    {
        public NewAppointmentRequestValidator()
        {
            RuleFor(z => z.AppointmentIdentifier)
                .NotEmpty()
                .WithMessage("AppointmentIdentifier can not be empty.");

            RuleFor(z => z.AppointmentType)
                .NotEmpty()
                .WithMessage("AppointmentType can not be empty.");

            RuleFor(z => z.AppointmentLocation)
                .NotEmpty()
                .WithMessage("AppointmentLocation can not be empty.");

            RuleFor(z => z.AppointmentLocationDescription)
                .NotEmpty()
                .WithMessage("AppointmentLocationDescription can not be empty.");

            RuleFor(z => z.AppointmentDateTime)
                .NotEmpty().WithMessage("AppointmentDateTime can not be empty.")
                .GreaterThan(TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("Arab Standard Time"))).WithMessage("AppointmentDateTime must be greater than now.");

            RuleFor(z => z.AppointmentArriveMinutesBefore)
                .NotEmpty()
                .WithMessage("AppointmentArriveMinutesBefore can not be empty.");

            RuleFor(z => z.CustomIdentifier)
                .NotEmpty().WithMessage("CustomIdentifier can not be empty.")
                .Length(10, 10).WithMessage("The CustomIdentitifer length must be 10 characters.")
                .Must(e => e.StartsWith("HC")).WithMessage("CustomIdentifier must starts with HC");

            RuleFor(z => z.SubjectIdentifier)
                .NotEmpty()
                .WithMessage("SubjectIdentifier can not be empty.");

            RuleFor(z => z.RecipientFirstName)
                .NotEmpty()
                .WithMessage("RecipientFirstName can not be empty.");

            RuleFor(z => z.RecipientLastName)
                .NotEmpty()
                .WithMessage("RecipientLastName can not be empty.");

            RuleFor(z => z.RecipientPhone)
                .NotEmpty()
                .WithMessage("RecipientPhone can not be empty.");

            RuleFor(z => z.SendDateTime)
                .LessThan(z => z.AppointmentDateTime).WithMessage("SendDateTime must be less than AppointmentDateTime.")
                .NotEmpty().WithMessage("SendDateTime can not be empty.");

            RuleFor(z => z.ReminderSendHoursBefore)
                .NotEmpty()
                .WithMessage("ReminderSendHoursBefore can not be empty.");

            RuleFor(z => z.ReminderType)
                .NotEmpty()
                .WithMessage("ReminderType can not be empty.");

            RuleFor(z => z.RecipientNationality)
                .NotEmpty()
                .WithMessage("RecipientNationality can not be empty.");
        }
    }
}
