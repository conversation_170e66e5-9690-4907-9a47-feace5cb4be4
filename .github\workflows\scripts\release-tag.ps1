param(
  [string]$release,
  [PSCustomObject]$github
 )
 $Time = Get-Date
$releaseDateTimeUTC =  $Time.ToUniversalTime()
$releaseDateTime = $releaseDateTimeUTC.ToString("yyyy-MM-dd HH:mm:ss")
$filePath = ".github\workflows\markdowns\release-tag.md"
$content = Get-Content -Path $filePath
$content = $content.Replace("{{release}}",$release)
$content = $content.Replace("{{actor}}",$github.actor)
$content = $content.Replace("{{time}}",$releaseDateTime)
$content = $content.Replace("{{commit_sha}}",$github.sha)
$content = $content.Replace("{{message}}",$github.message)
$content = $content.Replace("{{pr}}",$github.pr)
Set-Content -Path $filePath -Value $content
