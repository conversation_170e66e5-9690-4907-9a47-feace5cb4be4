﻿using Core.Results;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace WebAPI.Filters
{
    /// <summary>
    /// Request model validation filter
    /// </summary>
    public class ModelValidationFilter : IAsyncActionFilter
    {
        /// <summary>
        /// Execution check
        /// </summary>
        /// <param name="context"></param>
        /// <param name="next"></param>
        /// <returns></returns>
        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            if (!context.ModelState.IsValid)
            {
                var error = new APIErrorResult(APIErrorType.ValidationError, 
                    "One or more property are invalid.", 
                    context
                        .ModelState
                        .Where(z => z.Value != null && z.Value.Errors.Count > 0)
                        .ToDictionary(z => z.Key, z => z.Value.Errors.Select(a => a.ErrorMessage)));
                
                context.Result = new BadRequestObjectResult(error);
                return;
            }
            await next();
        }
    }

}
