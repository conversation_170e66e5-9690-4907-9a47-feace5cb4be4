name: Stage Deployment

on:

    workflow_call:

jobs:
  #stage secondary slot deployment
  StageSecondaryDeployment:
    name: 'Stage Secondary'
    permissions:
       id-token: write
       contents: read
    runs-on:  [self-hosted,linux]
    outputs:
     website: ${{ steps.get-website.outputs.website }} 
    environment:
       name: 'STAGE-SECONDARY'  
 
    steps:
       - name: Download artifact from build job
         uses: actions/download-artifact@v3
         with:
           name: ${{vars.BUILD_ARTIFACT_NAME}}   # place artifact name which was built by previous job
           path: ${{vars.BUILD_DOWNLOAD_PATH}}
 
        
       - name: Display appSettings content
         run: |
             cat ${{vars.BUILD_DOWNLOAD_PATH}}/${{vars.BUILD_APPSETTING_NAME}}
         
     
     # - name: Zip Updated Build
        # run: Compress-Archive -Path "*" stagewebapp.zip
        # shell: pwsh
 
     # uncomment below steps when go for Azure deployment
       - name: Sign in to Azure
         uses: azure/login@v1
       
         with:
           client-id: ${{ vars.AZURE_CLIENT_ID }}
           tenant-id: ${{ vars.AZURE_TENANT_ID }}
           subscription-id: ${{ vars.AZURE_SUBSCRIPTION_ID }}
       - name: 'Deploying to Azure Web App'
         uses: azure/webapps-deploy@v2
        # if: ${{false}}  # disable this step
         with: 
           app-name: ${{vars.AZURE_WEBAPP_NAME}}   # variable get from specific environment
           slot-name: ${{vars.DEPLOYMENT_SLOT}}    # variable get from specific environment
           package: ${{ vars.BUILD_DOWNLOAD_PATH }}  # repo variable
           

   #---------------Below code is used for compressing build artifacts into zip file: Required when go for zip deployment -------------------------------   
       - name: Package Build Artifact
         if: ${{false}}  # disable this step
         run: |
          Compress-Archive -Path "${{vars.BUILD_DOWNLOAD_PATH}}\*"  -Force  -DestinationPath build.zip 
         shell: pwsh
     #------------------------------------------------------------------------
                         

     #  - name:  Getting hostname
      #   id: generate-host-name
       #  run: |
        #      echo "websitename=$(az webapp config hostname list --resource-group  ${{vars.RESOURCE_GROUP_NAME}}  --webapp-name ${{vars.AZURE_WEBAPP_NAME}}  --slot ${{vars.DEPLOYMENT_SLOT}}   --query "[].name" --output tsv)" >> "$GITHUB_OUTPUT" 
      
       - name: Get Website
         id: get-website  
         run: |
             echo "website=${{vars.WEBSITE}}" >> "$GITHUB_OUTPUT"
 
       - name: Logout with Azure CLI script
         uses: azure/CLI@v1
         with:
             inlineScript: |
              az logout
              az cache purge
              az account clear   
       - name: Print Job Summary
         run:  echo "#### web app successfully deployed on ${{steps.get-website.outputs.website}} "  >>  $GITHUB_STEP_SUMMARY 

# Stage api regression test for secondary slot
  call-regression-stage-workflow:
       name: 'Secondary Slot Regression Test'
       needs: StageSecondaryDeployment
       uses: public-health-care-center-CORP/CancerScreeningSMS/.github/workflows/regression-api-test.yml@main
       secrets: inherit   #passing environment secrets
       with:
         collection_path1: ./api/Collection/*.postman_collection.json       
         report-path: 'Stage-Secondary'
         host-name: ${{needs.StageSecondaryDeployment.outputs.website}}
         suppress-exit-code: --bail  # --bail : stops runner when test case fails ;--suppress-exit-code for suppress
         data-path: ./api/Data/data-stage-secondary.json
         number-of-iteration: 1        
      

 
# Performs stage slot swap
  call-slot-swap-stage-workflow:
       name: 'Stage Slot Swap'
       needs: call-regression-stage-workflow
       uses: public-health-care-center-CORP/CancerScreeningSMS/.github/workflows/slot-swap.yml@main
       secrets: inherit   #passing environment secrets
       with:
         environment-e: 'STAGE'       
         

 # Perform API regression after secondary slot Swap 
  call-regression-after-stage-swap-workflow:
                    name: 'Regression Primary Slot Test'
                    needs: call-slot-swap-stage-workflow
                    uses: public-health-care-center-CORP/CancerScreeningSMS/.github/workflows/regression-api-test.yml@main
                    secrets: inherit   #passing environment secrets
                    with:
                      collection_path1: ./api/Collection/*.postman_collection.json      
                      host-name: ${{needs.call-slot-swap-stage-workflow.outputs.website}}
                      report-path: 'Stage'
                      suppress-exit-code: --bail # --bail : stops runner when test case fails  ; --suppress-exit-code for suppress
                      data-path: ./api/Data/data-stage.json
                      number-of-iteration: 1   
