﻿using Microsoft.OpenApi.Models;

namespace WebAPI.Setup
{
    public static class SwaggerSetup
    {
        public static IServiceCollection RunSwaggerSetup(this IServiceCollection services)
        {
            services.AddEndpointsApiExplorer();

            services.AddSwaggerGen(z =>
            {
                z.AddSecurityDefinition("ClientId", new OpenApiSecurityScheme
                {
                    Description = "ClientId must appear in header",
                    Type = SecuritySchemeType.ApiKey,
                    Name = "X-Client-Id",
                    In = ParameterLocation.Header,
                    Scheme = "ApiKeyScheme"
                });

                z.AddSecurityDefinition("SecretKey", new OpenApiSecurityScheme
                {
                    Description = "SecretKey must appear in header",
                    Type = SecuritySchemeType.ApiKey,
                    Name = "X-Secret-Key",
                    In = ParameterLocation.Header,
                    Scheme = "ApiKeyScheme"
                });

                var clientIdKey = new OpenApiSecurityScheme()
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "ClientId"
                    },
                    In = ParameterLocation.Header
                };

                var secretKeyKey = new OpenApiSecurityScheme()
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "SecretKey"
                    },
                    In = ParameterLocation.Header
                };

                var requirement = new OpenApiSecurityRequirement
                    {
                        { clientIdKey, new List<string>() },
                        { secretKeyKey, new List<string>() }
                    };

                z.AddSecurityRequirement(requirement);

            });

            return services;
        }
    }
}
