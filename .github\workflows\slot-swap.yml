#Slot swap - Performs Azure App Service Slot Swap - Source and Target 
name: 'Slot Swap' # performs slot swap
run-name: Slot Swap ${{github.run_number}} by @${{ github.actor }}
on:
    workflow_call:
             inputs:
                  environment-e:
                          required: false
                          type: string
                          default: 'STAGE'
             outputs:
                 website:
                   description: "application hostname"
                   value: ${{ jobs.SlotSwap.outputs.website }}
jobs:
  SlotSwap:
        name: 'Auto Slot Swap'
        # if: ${{ false }}  #disabled
        permissions:
          id-token: write
          contents: read
        runs-on: [self-hosted,linux]
        environment:
            name: ${{inputs.environment-e}}
        outputs:
                website: ${{ steps.get-website.outputs.website }} 
        steps:
            - name: Sign in to Azure
              uses: azure/login@v1
          
              with:
                client-id: ${{ vars.AZURE_CLIENT_ID }}
                tenant-id: ${{ vars.AZURE_TENANT_ID }}
                subscription-id: ${{ vars.AZURE_SUBSCRIPTION_ID }}
            - name: Swap Slots 
              run: |
                az webapp deployment slot swap  -g ${{vars.RESOURCE_GROUP_NAME}} -n  ${{vars.AZURE_WEBAPP_NAME}} --slot ${{vars.DEPLOYMENT_SLOT}} --target-slot ${{ vars.TARGET_SLOT_NAME }}
              shell: pwsh
            
            - name: Logout with Azure CLI script
              uses: azure/CLI@v1
              with:
                  inlineScript: |
                   az logout
                   az cache purge
                   az account clear
            - name: Get Website
              id: get-website  
              run: |
                       echo "website=${{vars.WEBSITE}}" >> "$GITHUB_OUTPUT"
