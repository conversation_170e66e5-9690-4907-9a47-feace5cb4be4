
# minimal yaml approach.
# repo contains trigger definition and simple workflow call.
name: CancerScreening-SMS CI-CD
run-name: Release-1.0.${{github.run_number}} by @${{ github.actor }}
on:
    workflow_dispatch: # disbale this trigger after testing
    push:
            branches:
              - 'main'
            paths-ignore:
              - 'DBScripts/**'
              - '.github/**'
              - 'README.md'
jobs:
    cicd-job:
        name: CancerScreenSMS CICD
        uses: public-health-care-center-CORP/WorkflowRepo/.github/workflows/ci-cd-cancerscreening-sms.yml@main
        secrets: inherit