﻿using Core.Configurations;
using Core.Exceptions;
using Microsoft.AspNetCore.Mvc.Filters;

namespace WebAPI.Filters
{

    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class AuthorizeFilter : Attribute, IAuthorizationFilter
    {
        private readonly IConfiguration _configuration;

        public AuthorizeFilter(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            if (!context.HttpContext.Request.Headers.TryGetValue("X-Client-Id", out var clientId) || string.IsNullOrEmpty(clientId))
                throw new AuthenticationException("X-Client-Id parameter is missign.");

            if (!context.HttpContext.Request.Headers.TryGetValue("X-Secret-Key", out var secretKey) || string.IsNullOrEmpty(secretKey))
                throw new AuthenticationException("X-Secret-Key parameter is missign.");

            var users = _configuration.GetSection("Users").Get<IEnumerable<UserConfig>>();
            var user = users.FirstOrDefault(z => z.ClientId == clientId.ToString() && z.SecretKey == secretKey.ToString());

            if (user is null)
                throw new AuthenticationException("ClientId or SecretKey is invalid.");

            if(!user.IsActive)
                throw new AuthenticationException("This account has been disabled.");
        }
    }
}
