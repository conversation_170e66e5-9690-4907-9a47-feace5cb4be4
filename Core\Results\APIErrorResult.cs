﻿using System.Text.Json.Serialization;

namespace Core.Results
{
    public enum APIErrorType
    {
        None,
        GeneralError,
        ValidationError,
        BusinessError,
        Authentication
    }

    public class APIErrorResult : IAPIErrorResult
    {
        public int ErrorType { get; private set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? ErrorTypeDescription { get; private set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? Message { get; private set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? StackTrace { get; private set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public Dictionary<string, IEnumerable<string>>? ValidationErrors { get; private set; }

        public APIErrorResult(APIErrorType type = APIErrorType.None)
        {
            ErrorType = (int)type;
            ErrorTypeDescription = type.ToString();
        }

        public APIErrorResult(APIErrorType type = APIErrorType.None, string? message = "")
        {
            ErrorType = (int)type;
            ErrorTypeDescription = type.ToString();
            Message = message;
        }

        public APIErrorResult(APIErrorType type = APIErrorType.None, string? message = "", 
            string? stackTrace = "")
        {
            ErrorType = (int)type;
            ErrorTypeDescription = type.ToString();
            Message = message;
            StackTrace = stackTrace;
        }

        public APIErrorResult(APIErrorType type = APIErrorType.None, string message = "", 
            Dictionary<string, IEnumerable<string>>? validationErrors = null)
        {
            ErrorType = (int)type;
            ErrorTypeDescription = type.ToString();
            Message = message;
            ValidationErrors = validationErrors;
        }

        public APIErrorResult(APIErrorType type = APIErrorType.None, string message = "",
            Dictionary<string, IEnumerable<string>>? validationErrors = null, string? stackTrace = "")
        {
            ErrorType = (int)type;
            ErrorTypeDescription = type.ToString();
            Message = message;
            ValidationErrors = validationErrors;
            StackTrace = stackTrace;
        }
    }
}
