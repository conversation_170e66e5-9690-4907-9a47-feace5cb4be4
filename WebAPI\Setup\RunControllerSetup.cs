﻿// using WebAPI.Filters;

namespace WebAPI.Setup
{
    public static class ControllerSetup
    {
        public static IServiceCollection RunControllerSetup(this IServiceCollection services)
        {
            services
                .AddControllers(z =>
                {
                    // z.Filters.Add<ModelValidationFilter>(); NOTE: FluentValidation kullanıldığından kapatıldı.
                })
                .AddJsonOptions(z =>
                {
                    z.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
                })
                .ConfigureApiBehaviorOptions(z =>
                {
                    z.SuppressModelStateInvalidFilter = true;
                });

            return services;
        }
    }
}
