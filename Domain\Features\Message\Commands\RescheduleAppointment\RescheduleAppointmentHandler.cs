﻿using Core.Constants;
using Core.Dynamics;
using Core.Exceptions;
using FluentValidation;
using MediatR;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;

namespace Domain.Features.Message.Commands.RescheduleAppointment
{
    public class RescheduleAppointmentHandler : IRequestHandler<RescheduleAppointmentRequest, RescheduleAppointmentResponse>
    {
        private readonly ServiceClient _service;
        private readonly IValidator<RescheduleAppointmentRequest> _validator = new RescheduleAppointmentValidator();
        private readonly MessageManager _messageManager;
        private Entity? patient;
        private Entity? location;
        public RescheduleAppointmentHandler(ServiceClient service, MessageManager messageManager)
        {
            _service = service;
            _messageManager = messageManager;
        }

        public async Task<RescheduleAppointmentResponse> Handle(RescheduleAppointmentRequest request, CancellationToken cancellationToken)
        {
            _validator.ValidateAndThrow(request);

            location = _messageManager.GetLocationByCode(request.AppointmentLocation);

            if (location == null)
            {
                throw new BusinessException("Health centre does not exist on the lookup table for Cancer Screening SMS System.");
            }
            patient = _messageManager.GetPatientByHealthCardNumber(request.CustomIdentifier);
            await UpdateMessage(request.AppointmentIdentifier, request.CustomIdentifier);
            var id = await CreateMessage(request, MessageType.RescheduleAppointment);
            if (_messageManager.IsAppointmentReminder(request.AppointmentDateTime, request.ReminderSendHoursBefore))
            {
                await CreateMessage(request, MessageType.AppointmentReminder);
            }
            var message = _service.Retrieve("phcc_message", id, new ColumnSet("phcc_messageid"));

            var returnValue = new RescheduleAppointmentResponse()
            {
                Id = id,
                MessageId = message.GetString("phcc_messageid")
            };

            return returnValue;
        }

        private async Task UpdateMessage(string appointmentIdentifier, string customIdentifier)
        {
            List<Entity> entities = await GetUnprocessedMessages(appointmentIdentifier);
            if (entities.Count > 0)
            {
                foreach (Entity entity in entities)
                {
                    if (entity.GetString("phcc_recipienthealthcardnumber") != customIdentifier)
                    {
                        throw new BusinessException("The appointment that you are trying to reschedule/cancel seems to belong to another patient. Please contact with your administrator");
                    }
                    entity["statecode"] = new OptionSetValue((int)ActivityStatus.Cancelled);
                    entity["statuscode"] = new OptionSetValue((int)MessageStatus.Cancelled);
                    await _service.UpdateAsync(entity);
                }
            }
            //else
            //{
            //    throw new BusinessException("There is no active appointment to reschedule. Please create a new appointment.");
            //}
        }

        private async Task<Guid> CreateMessage(RescheduleAppointmentRequest request, MessageType messageType)
        {
            var entity = new Entity("phcc_message");

            if (patient != null)
            {
                entity["regardingobjectid"] = new EntityReference(patient.LogicalName, patient.Id);
            }
            string language = _messageManager.NationalityCheck(request.RecipientNationality);
            entity["phcc_id"] = request.AppointmentIdentifier;
            entity["phcc_appointmenttype"] = request.AppointmentType;
            entity["phcc_locationid"] = new EntityReference(location.LogicalName, location.Id);
            entity["phcc_locationdescription"] = language == "English" ? location.GetString("msemr_text") : location.GetString("phcc_textarabic");
            entity["phcc_appointmentdatetime"] = request.AppointmentDateTime;
            entity["phcc_arriveminutesbefore"] = request.AppointmentArriveMinutesBefore;
            entity["phcc_recipientfirstname"] = request.RecipientFirstName;
            entity["phcc_recipientlastname"] = request.RecipientLastName;
            entity["phcc_recipientphone"] = request.RecipientPhone;
            entity["phcc_emailaddress"] = request.RecipientEmail;
            entity["phcc_scheduleddate"] = messageType == MessageType.AppointmentReminder ? request.AppointmentDateTime.AddHours(-request.ReminderSendHoursBefore) : request.SendDateTime;
            entity["phcc_reminderhoursbefore"] = request.ReminderSendHoursBefore;
            entity["phcc_recipientnationality"] = language;
            entity["phcc_recipienthealthcardnumber"] = request.CustomIdentifier;
            entity["statecode"] = new OptionSetValue((int)ActivityStatus.Scheduled);
            entity["statuscode"] = new OptionSetValue((int)MessageStatus.WaitingSend);
            entity["phcc_messagetype"] = new OptionSetValue((int)messageType);

            var id = await _service.CreateAsync(entity);

            return id;
        }

        private async Task<List<Entity>> GetUnprocessedMessages(string appointmentIdentifier)
        {
            QueryExpression queryExpression = new QueryExpression();
            queryExpression.EntityName = "phcc_message";
            queryExpression.NoLock = true;
            queryExpression.Criteria.AddCondition("phcc_id", ConditionOperator.Equal, appointmentIdentifier);
            queryExpression.Criteria.AddCondition("phcc_appointmentdatetime", ConditionOperator.GreaterThan, TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("Arab Standard Time")));
            queryExpression.Criteria.AddCondition("statuscode", ConditionOperator.In, (int)MessageStatus.WaitingSend, (int)MessageStatus.Retry, (int)MessageStatus.Error);
            queryExpression.Criteria.AddCondition("phcc_messagetype", ConditionOperator.In, (int)MessageType.NewAppointment, (int)MessageType.RescheduleAppointment, (int)MessageType.AppointmentReminder);
            queryExpression.ColumnSet = new ColumnSet("phcc_recipienthealthcardnumber");
            EntityCollection collection = await _service.RetrieveMultipleAsync(queryExpression);
            return collection.Entities.ToList();
        }
    }
}
