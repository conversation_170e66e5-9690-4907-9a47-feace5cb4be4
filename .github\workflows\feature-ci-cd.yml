#Summary:
 # Feature Branch Pipeline - It triggered when new feature branch created from main branch and whenver new push commits added into feature branch
 # Below are the jobs
 # Build - Build the application and produces artifact
 # Deployment : deploy artifact into dev environment - deploy branch into app service's dedicated feature branch slots 

name: Feature Branch Build and Deploy
# triggred when branch created, committed
run-name: Feature Branch - ${{github.run_number}} by @${{ github.actor }}
on:
 
   push:
      branches:
        - 'feature/main/**'
      paths-ignore:
        - 'Build/**'
        - '.github/**'
        - 'README.md'
        - 'Test/**'
   workflow_call:  # call when feature branch created

jobs:

 build:
    permissions:
               id-token: write
               contents: write
               checks: write
               pull-requests: write
    runs-on: ubuntu-latest
    # if: "contains(github.event.head_commit.message, '[deploy]')" # run only when feature/main/** branch commit message contains [deploy]  -- keeping this line for reference
    steps:
       - name : Checkout Branch
         uses: actions/checkout@v3
         
       - name: Setup .NET 
         uses: actions/setup-dotnet@v3
         with:
          dotnet-version: ${{vars.BUILD_DOTNET_CORE_VERSION}} 

       - name: Restore dependencies
         run:  dotnet restore ${{vars.BUILD_WORKING_DIRECTORY}}  
      
       - name: Build
         run:  dotnet build "${{ vars.BUILD_WORKING_DIRECTORY }}" --configuration ${{ vars.BUILD_CONFIGURATION }} --no-restore
      
       - name: Publish
         run:  dotnet publish "${{ vars.BUILD_WORKING_DIRECTORY }}" --configuration ${{ vars.BUILD_CONFIGURATION }} --no-build --output "${{ vars.BUILD_WEBAPP_PACKAGE_PATH }}"
       
       - name: Upload Artifact
         uses: actions/upload-artifact@v3.1.2
         with:
          name: ${{vars.BUILD_ARTIFACT_NAME}}
          path: ${{ vars.BUILD_WEBAPP_PACKAGE_PATH }} 
          if-no-files-found: error  
          
          #-----------------------------------------Start Of Feature deployment on Dev environment        
 dev-deployment:
   
   name: 'Feature Deployment'
   needs: build
   environment:
      name: 'DEV'
   permissions:
      id-token: write
      contents: read
   runs-on:  [self-hosted,linux]
   outputs:
         hostname: ${{ steps.generate-host-name.outputs.websitename }}
    
  # if: ${{ false }} # disabled job
   steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v3
        with:
          name: ${{vars.BUILD_ARTIFACT_NAME}}   # place artifact name which was built by previous job
          path: ${{vars.BUILD_DOWNLOAD_PATH}}
      
      - name: Generate Slot Name
        id: generate-slot-name
        run : |
            $branchName="${{github.ref_name}}"
            $branchName=$branchName -replace "/", "" -replace "-",""
            echo $branchName
            echo "SLOT_NAME=$branchName" >>  $env:GITHUB_OUTPUT
        shell: pwsh
     
      - name: Updating Dynamics URL
        uses: microsoft/variable-substitution@v1
        with:
         files: '${{vars.BUILD_DOWNLOAD_PATH}}/${{vars.BUILD_APPSETTING_NAME}}'
        env:
         Dynamics.OrganizationURI: ${{ vars.DYNAMICS_ORGANIZATIONURI }}  
         Dynamics.TenantId: ${{ vars.DYNAMICS_TENANTID }}
         Dynamics.ClientId: ${{ vars.DYNAMICS_CLIENTID }}
         Dynamics.SecretKey: ${{ secrets.DYNAMICS_SECRETKEY }}
         Dynamics.RedirectURI: ${{ vars.DYNAMICS_REDIRECTURI }}
       
      - name: Upload Feature Artifact
        uses: actions/upload-artifact@v3.1.2
        with:
          name: 'featurewebapp'
          path:  ${{vars.BUILD_DOWNLOAD_PATH}}
          if-no-files-found: error
          
   #---------------Below code is used for compressing build artifacts into zip file: Required when go for zip deployment -------------------------------   
     # - name: Package Build Artifact
      #  run: |
       #   Compress-Archive -Path "${{vars.BUILD_DOWNLOAD_PATH}}\*"  -Force  -DestinationPath build.zip 
        #shell: pwsh
     #------------------------------------------------------------------------
      - name: List files
        run: |
          ls
        shell: pwsh

    #---------------Read from azurefile.txt -------------------------------
     # - name: Get Feature Branch slot name for deployment
      #  run: |
       #    $firstLineFromFile = Get-Item -Path .\azurefile.txt | Get-Content -First 1
        #   $hostname = $firstLineFromFile -split '(=)'
         #  echo $hostname
        
   #----------------------end of reference code ----------------------------------
          
          
    # uncomment below steps when go for Azure deployment
      - name: Sign in to Azure
        uses: azure/login@v1
      
        with:
          client-id: ${{ vars.AZURE_CLIENT_ID }}
          tenant-id: ${{ vars.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.AZURE_SUBSCRIPTION_ID }}

#-----------------------------Commented CLI Based Deployment---------------------------------------------------
     # - name: Deploying to webapp via Azure CLI
      #  id: deploy-slot
       # run: |
        #   az webapp deployment source config-zip --src build.zip   --name ${{vars.AZURE_WEBAPP_NAME}} --resource-group ${{vars.RESOURCE_GROUP_NAME}} --slot ${{steps.generate-slot-name.outputs.SLOT_NAME}}

      #  shell: pwsh
    #--------------------------------------------------------------       
   
      - name: 'Deploying to Azure Web App'
        uses: azure/webapps-deploy@v2
        with: 
          app-name: ${{vars.AZURE_WEBAPP_NAME}}   # variable get from specific environment
          slot-name: ${{steps.generate-slot-name.outputs.SLOT_NAME}} 
          package: ${{ vars.BUILD_DOWNLOAD_PATH }}  # repo variable
   
      - name:  Getting hostname
        id: generate-host-name
        run: |
           echo "websitename=$(az webapp config hostname list --resource-group  ${{vars.RESOURCE_GROUP_NAME}}  --webapp-name ${{vars.AZURE_WEBAPP_NAME}}  --slot ${{steps.generate-slot-name.outputs.SLOT_NAME}}   --query "[].name" --output tsv)" >> "$GITHUB_OUTPUT" 
   
      - name: Logout with Azure CLI script
        uses: azure/CLI@v1
        with:
          inlineScript: |
           az logout
           az cache purge
           az account clear
   
# -------------------------------------------------------End of DEV Deployment---------------------------- 







