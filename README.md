### PHCC CancerSMS API :    [![Cancer Screening CI/CD Pipeline](https://github.com/public-health-care-center-CORP/CancerScreeningSMS/actions/workflows/app-CancerScreeningSMS-ci.yml/badge.svg?branch=main)](https://github.com/public-health-care-center-CORP/CancerScreeningSMS/actions/workflows/app-CancerScreeningSMS-ci.yml)
#### 1.	Summary
Cancer Screening SMS is an API service which primary job is to process upstream system's (Fuji Synapse) SMS request. In order to complete the request, system relies on Dynamics Data verse system, Power Platform system and third-party SMS vendor called Ooredoo for delivering messages to recipients.
#### 2.	Technology Stack:
- Language: C#.NET
- Framework: .NET 6 Web API
#### 3.	Dependencies:
-	Dynamics 365 Dataverse
-	Power Automate- Not direct dependency but included in monitoring scope.
-	Ooredoo SMS API– Not direct dependency but included in monitoring scope.
________________________________________
`Note: Operations and maintenance documentation moved to Wiki.`



