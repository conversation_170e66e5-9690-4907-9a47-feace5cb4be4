﻿using FluentValidation;

namespace Domain.Features.Message.Queries.GetAppointmentDetailById
{
    public class GetAppointmentDetailByIdRequestValidator : AbstractValidator<GetAppointmentDetailByIdRequest>
    {
        public GetAppointmentDetailByIdRequestValidator()
        {
            RuleFor(z => z.Id)
                .NotEmpty()
                .WithMessage("Id can not be empty.");
        }
    }
}
