﻿using Core.Contracts;
using Microsoft.Xrm.Sdk;

namespace Core.Dynamics
{
    public static class EntityExtensions
    {
        public static void ConvertAliasedValues(this Entity entity)
        {
            foreach (var attr in entity.Attributes.Where(z => z.Value is AliasedValue))
                entity[attr.Key] = (attr.Value as AliasedValue).Value;
        }

        public static Lookup GetLookup(this Entity entity, string attributeLogicalName)
        {
            if (!entity.Contains(attributeLogicalName))
                return null;

            return new Lookup
            {
                Id = entity.GetAttributeValue<EntityReference>(attributeLogicalName).Id,
                Name = entity.GetAttributeValue<EntityReference>(attributeLogicalName).Name
            };
        }
        public static Lookup GetLookup(this Entity entity, string alias, string attributeLogicalName)
        {
            var logicalName = $"{alias}.{attributeLogicalName}";
            if (!entity.Contains(logicalName))
                return null;

            return new Lookup
            {
                Id = ((EntityReference)entity.GetAttributeValue<AliasedValue>(logicalName).Value).Id,
                Name = ((EntityReference)entity.GetAttributeValue<AliasedValue>(logicalName).Value).Name
            };
        }

        public static string GetString(this Entity entity, string attributeLogicalName)
        {
            if (!entity.Contains(attributeLogicalName))
                return null;

            return entity.GetAttributeValue<string>(attributeLogicalName);
        }
        public static string GetString(this Entity entity, string alias, string attributeLogicalName)
        {
            var logicalName = $"{alias}.{attributeLogicalName}";
            if (!entity.Contains(logicalName))
                return null;

            return entity.GetAttributeValue<AliasedValue>(logicalName).Value.ToString();
        }

        public static int? GetInt(this Entity entity, string attributeLogicalName)
        {
            if (!entity.Contains(attributeLogicalName))
                return (int?)null;

            return entity.GetAttributeValue<int>(attributeLogicalName);
        }
        public static int? GetInt(this Entity entity, string alias, string attributeLogicalName)
        {
            var logicalName = $"{alias}.{attributeLogicalName}";
            if (!entity.Contains(logicalName))
                return (int?)null;

            return (int)entity.GetAttributeValue<AliasedValue>(logicalName).Value;
        }

        public static decimal? GetDecimal(this Entity entity, string attributeLogicalName)
        {
            if (!entity.Contains(attributeLogicalName))
                return (decimal?)null;

            return entity.GetAttributeValue<decimal>(attributeLogicalName);
        }
        public static decimal? GetDecimal(this Entity entity, string alias, string attributeLogicalName)
        {
            var logicalName = $"{alias}.{attributeLogicalName}";
            if (!entity.Contains(logicalName))
                return (decimal?)null;

            return (decimal)entity.GetAttributeValue<AliasedValue>(logicalName).Value;
        }

        public static decimal? GetMoney(this Entity entity, string attributeLogicalName)
        {
            if (!entity.Contains(attributeLogicalName))
                return (decimal?)null;

            return entity.GetAttributeValue<Money>(attributeLogicalName).Value;
        }
        public static decimal? GetMoney(this Entity entity, string alias, string attributeLogicalName)
        {
            var logicalName = $"{alias}.{attributeLogicalName}";
            if (!entity.Contains(logicalName))
                return (decimal?)null;

            return ((Money)(entity.GetAttributeValue<AliasedValue>(logicalName).Value)).Value;
        }

        public static float? GetFloat(this Entity entity, string attributeLogicalName)
        {
            if (!entity.Contains(attributeLogicalName))
                return (float?)null;

            return entity.GetAttributeValue<float>(attributeLogicalName);
        }
        public static float? GetFloat(this Entity entity, string alias, string attributeLogicalName)
        {
            var logicalName = $"{alias}.{attributeLogicalName}";
            if (!entity.Contains(logicalName))
                return (float?)null;

            return (float)entity.GetAttributeValue<AliasedValue>(logicalName).Value;
        }

        public static double? GetDouble(this Entity entity, string attributeLogicalName)
        {
            if (!entity.Contains(attributeLogicalName))
                return (double?)null;

            return entity.GetAttributeValue<double>(attributeLogicalName);
        }
        public static double? GetDouble(this Entity entity, string alias, string attributeLogicalName)
        {
            var logicalName = $"{alias}.{attributeLogicalName}";
            if (!entity.Contains(logicalName))
                return (double?)null;

            return (double)entity.GetAttributeValue<AliasedValue>(logicalName).Value;
        }

        public static DateTime? GetDateTime(this Entity entity, string attributeLogicalName)
        {
            if (!entity.Contains(attributeLogicalName))
                return (DateTime?)null;

            return entity.GetAttributeValue<DateTime>(attributeLogicalName);
        }
        public static DateTime? GetDateTime(this Entity entity, string alias, string attributeLogicalName)
        {
            var logicalName = $"{alias}.{attributeLogicalName}";
            if (!entity.Contains(logicalName))
                return (DateTime?)null;

            return (DateTime)entity.GetAttributeValue<AliasedValue>(logicalName).Value;
        }

        public static OptionSet GetOptionSet(this Entity entity, string attributeLogicalName)
        {
            if (!entity.Contains(attributeLogicalName))
                return null;

            return new OptionSet
            {
                Value = entity.GetAttributeValue<OptionSetValue>(attributeLogicalName).Value,
                Label = entity.FormattedValues[attributeLogicalName]
            };
        }
        public static OptionSet GetOptionSet(this Entity entity, string alias, string attributeLogicalName)
        {
            var logicalName = $"{alias}.{attributeLogicalName}";
            if (!entity.Contains(logicalName))
                return null;

            return new OptionSet
            {
                Value = ((OptionSetValue)entity.GetAttributeValue<AliasedValue>(logicalName).Value).Value,
                Label = entity.FormattedValues[logicalName]
            };
        }

        public static bool? GetBoolean(this Entity entity, string attributeLogicalName)
        {
            if (!entity.Contains(attributeLogicalName))
                return (bool?)null;

            return entity.GetAttributeValue<bool>(attributeLogicalName);
        }
        public static bool? GetBoolean(this Entity entity, string alias, string attributeLogicalName)
        {
            var logicalName = $"{alias}.{attributeLogicalName}";
            if (!entity.Contains(logicalName))
                return (bool?)null;

            return Convert.ToBoolean(entity.GetAttributeValue<AliasedValue>(logicalName).Value);
        }

        public static Guid? GetKey(this Entity entity, string attributeLogicalName)
        {
            if (!entity.Contains(attributeLogicalName))
                return (Guid?)null;

            return entity.GetAttributeValue<Guid>(attributeLogicalName);
        }
        public static Guid? GetKey(this Entity entity, string alias, string attributeLogicalName)
        {
            var logicalName = $"{alias}.{attributeLogicalName}";
            if (!entity.Contains(logicalName))
                return (Guid?)null;

            return (Guid)(entity.GetAttributeValue<AliasedValue>(logicalName).Value);
        }
    }
}
