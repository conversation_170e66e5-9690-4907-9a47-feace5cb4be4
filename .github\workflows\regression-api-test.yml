# REFERENCE CODE: Please do not delete
#   newman run  ./api/Collection/Authentication.postman_collection.json  --insecure  --suppress-exit-code --env-var "hostname=https://app-cancerscreeningsms-tst02-secondary.azurewebsites.net"   -r htmlextra --reporter-htmlextra-export ./api/Reports/Stage/${{github.run_number}}/$timestamp-authentication.html   --reporter-htmlextra-darkTheme  ./api/Reports/Stage/${{github.run_number}}/$timestamp-authentication-runner-report.html 
# The code below does the following:
#1. Checkout the test branch
#2. Runs postman collections
#3. If any test case fails, it will stop the runner
#4. Uploads the run report as an artifact
#5. Commits the run report
#6. Deletes the api folder
name: API Regression Test 
on:
  workflow_dispatch: #display this trigger after testing completed.
  workflow_call:
           inputs:
                collection_path1:
                     description: 'postman collections folder path'
                     default: './api/Collection/*.postman_collection.json '
                     required: true
                     type: string
                suppress-exit-code:
                       required: false
                       type: string
                       default: --bail  #  --bail : stops runner when test case fails ; --suppress-exit-code - continue runner execution

                report-path: 
                        required: false
                        type: string
                        default: Stage      
                host-name:
                       required: true
                       type: string
                       default: 'https://stgcancerscreeningsms02.phcc.gov.qa'
                data-path:
                        description: 'New man json data file path'
                        required: false
                        default: ' ./api/Data/data-stage.json'
                        type: string
                number-of-iteration:
                         required: false
                         type: number
                         default: 1
                       
jobs:

 api-regression:

   permissions:
      id-token: write
      contents: write
   
   runs-on:  [self-hosted,windows]
   steps:

    - name: Checkout
      uses: actions/checkout@v3
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        ref: test  # checkout test branch
        fetch-depth: 0 # fetch all history commits - by default action checkouts only commits that were made before workflow trigger
    
    -  name: Update data file
       uses: ./.github/actions/DataUpdate
       with:
            file-path: ${{inputs.data-path}}

    - name: Commit Data file update to branch
      run: |
        git add ${{inputs.data-path}}
        git config --global user.name "kamil hussain"
        git config --global user.email "<EMAIL>"
        git commit -m   "updated data file - ${{github.run_number}}"
        git pull --rebase
        git push


    - name: Run Postman Collections 
      run: |
          $timestamp = Get-Date -Format "ddMMyyyyhhss"
          $collectionFiles = Get-ChildItem ${{inputs.collection_path1}} -Recurse
          foreach($f in $collectionFiles)
          {
            echo "executing $f"
            $resultFile = "./api/Reports/${{inputs.report-path}}/${{github.run_number}}/$timestamp-"+ $f.BaseName + ".html"
            newman run $f   --insecure  ${{inputs.suppress-exit-code}} --iteration-count ${{inputs.number-of-iteration}} --env-var "hostname=${{inputs.host-name}}"  --env-var "X-Client-Id=${{vars.API_CLIENT_ID}}"  --env-var "X-Secret-Key=${{secrets.API_SECRET_KEY}}"   -d ${{inputs.data-path}}     --reporters cli,htmlextra --reporter-htmlextra-export   $resultFile  --reporter-htmlextra-darkTheme  --reporter-htmlextra-title "Cancer Screening Automated Integration Report: ENV - ${{inputs.report-path}} "
              if(!$?)
              {
                echo "test cases failed for collection- $f"
                echo "Stopping the runner."
                $LastExitCode = 1
                break
              }  
          } 
      shell: pwsh

    - name: Output the Run Report
      uses: actions/upload-artifact@v2
      if: always()
      with: 
         name: RunReports
         path: |
            ./api/Reports/${{inputs.report-path}}/${{github.run_number}}
           
    - name: Commit Run Reports
      if: always()
      run: |
             git add api/Reports/${{inputs.report-path}}/${{github.run_number}}
             git config --global user.name "kamil hussain"
             git config --global user.email "<EMAIL>"
             git commit -m "added api testing report for run - ${{github.run_number}}"
             git pull --rebase
             git push

    - name: Delete api folder
      if: always()
      run: |
          if (Test-Path -Path api) {
            echo "api directory exists"
            echo "deleting api folder"
            Remove-Item api -Recurse
            }
      shell: pwsh

    - name: Print Job Summary
      run:  echo "#### Postman- Automated regression completed for ${{inputs.host-name}}. <br /> Please find the detailed report at <a href='https://github.com/public-health-care-center-CORP/CancerScreeningSMS/tree/test/api/Reports/${{inputs.report-path}}/${{github.run_number}}'>Report Links </a>"  >>  $GITHUB_STEP_SUMMARY 
