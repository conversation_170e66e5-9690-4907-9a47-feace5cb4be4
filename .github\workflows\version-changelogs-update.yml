name: Version & Changelog Update
on:
# pull_request:
 #    branches:
  #    - 'main'
 workflow_dispatch:
env:
  CONFIGURATION: Release
  DOTNET_CORE_VERSION: 6.0.x
  WORKING_DIRECTORY: WebAPI

jobs:
  test-job:
    runs-on: windows-latest
    steps:
        - name: Branch Names
          id: get-current-branch
         
         
        - name : Checkout Feature  Branch
          uses: actions/checkout@v3
          with:
           ref: ${{ steps.get-current-branch.outputs.current_branch }}
       
        - name : Rebase Build folder
          run: |
              git fetch
              git checkout origin/main Build
          
        - name:  Generate Version Number      
          id: generate-version-number
          run: |
            $applicationVersion = Get-Content -Path Build/VERSION.txt  -First 1
            echo "Application Current Version : $applicationVersion" 
            $splitVersion = $applicationVersion.split(".")
            $getPatchVersion = [int]$splitVersion[-1] 
            echo "Current Patch Version: $getPatchVersion"
            $updatedPatchVersion = $getPatchVersion + 1
            $updatedVersionArray = @($splitVersion[0],$splitVersion[1],$updatedPatchVersion)
            $updatedVersion = $updatedVersionArray -join '.'
            echo $updatedVersion
            Set-Content -Path Build/VERSION.txt -Value $updatedVersion
            echo "build version successfully updated"
            echo  "AppVersion=$updatedVersion" >>  $env:GITHUB_OUTPUT
             
          shell: pwsh
      
        - name: Update Release logs
          run: |
             $timestamp= Get-Date -Format "dddd MM/dd/yyyy HH:mm"
             $content = "`n ${{steps.generate-version-number.outputs.AppVersion}} DateTime: $timestamp  `n commit message=${{github.event.head_commit.message}}"  
             Add-Content Build/RELEASE.md $content
          shell: pwsh
      
        - name: Commit Version and Release logs
          run: |
                  git add Build/VERSION.txt
                  git add Build/RELEASE.md
                  git config --global user.name "kamil hussain"
                  git config --global user.email "<EMAIL>"
                  git commit -m "version and RELEASE log updated.[ci skip]"
                  git push
     #----------------------------------------------end of release, version update 
