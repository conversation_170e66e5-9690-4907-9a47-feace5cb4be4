#The code above does the following, explained in English:
# 1. On a feature branch deletion, the workflow will be triggered.
# 2. The workflow will delete the slot associated with the feature branch.
# shell: pwsh cross-platform powershell script which runs on windows as well Linux
name: Delete App Service Slot
# triggred when branch created, committed
run-name: Delete App Service Slot - ${{github.event.ref}} - ${{github.run_number}} by @${{ github.actor }} 
on:
 
 delete:
      branches:
       - 'feature/main/**'  # branch filtering not working 
      
jobs:
     
  featureslotdeletion:
   if: ${{ contains(github.event.ref, 'feature/main/') }} # exceute when feature/main deleted 
   name: 'Delete Feature Slot'
   environment:
      name: 'DEV'
   permissions:
      id-token: write
      contents: read
   runs-on:  [self-hosted,linux]
   steps:
          
    # uncomment below steps when go for Azure deployment
    
      - name: Sign in to Azure
        id: login-azure
        uses: azure/login@v1
      
        with:
          client-id: ${{ vars.AZURE_CLIENT_ID }}
          tenant-id: ${{ vars.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.AZURE_SUBSCRIPTION_ID }}
     
#------------- working bash script - commented for future referernces- we use only powershell script for all language related work. 
      #- name: Generate Slot Name
      #  id: generate-slot-name
       # run: |
        #    branchName=${{github.ref_name}}
         #   branchName="$branchName" | tr -d /  #remove slashes
          #  branchName="$branchName" | tr -d -   # remove hypens
           # echo $branchName
           # echo "SLOT_NAME=$branchName" >> $GITHUB_OUTPUT

      #- name: Create Slot
       # id: create-slot
        #run: |
         #   slotname=${{steps.test-1.outputs.SLOT_NAME}}
          #  echo $slotname
           # slothostname=$(az webapp deployment slot create --name ${{vars.AZURE_WEBAPP_NAME}} --resource-group ${{vars.RESOURCE_GROUP_NAME}} --slot $slotname --query "defaultHostName")
            #echo $slothostname
            #echo "SLOT_HOST_NAME=$slothostname" >> $GITHUB_OUTPUT            


      #- name: Update Azure File
       # run: |
        #  echo "SLOT_NAME=${{ steps.generate-slot-name.outputs.SLOT_NAME }}" > azure.txt
         # echo "SLOT_HOST_NAME=${{steps.create-slot.outputs.SLOT_HOST_NAME}}" >> azure.txt
 #-------------------------------------------------End of reference code----------------------------------         
 
      - name: Generate Slot Name
        id: generate-slot-name
        run : |
            $branchName="${{github.event.ref}}" # gives deleted branch name - which branch trigger this event
            $branchName=$branchName -replace "/", "" -replace "-",""
            echo $branchName
            echo "SLOT_NAME=$branchName" >>  $env:GITHUB_OUTPUT
        shell: pwsh
       
      - name: Delete Slot
        id: delete-slot
        run: |
            $slotname="${{steps.generate-slot-name.outputs.SLOT_NAME}}"
            echo $slotname
            $slothostname=$(az webapp deployment slot delete --name ${{vars.AZURE_WEBAPP_NAME}} --resource-group ${{vars.RESOURCE_GROUP_NAME}} --slot ${{steps.generate-slot-name.outputs.SLOT_NAME}}  --output tsv)
            echo $slothostname
            echo "slot successfully deleted"
        shell: pwsh
       
     #------------------------Commented File reference code -----------------------
     
     # - name: Update Azure file
      #  run: |
       #    $filePath ="azurefile.txt" # move this file path to env
        #   if (Test-Path $filePath) 
         #  {
          # Remove-Item $filePath
          # echo "deleted file: $filePath"
          # }
          # New-Item $filePath -ItemType File -Value "SLOT_NAME=${{ steps.generate-slot-name.outputs.SLOT_NAME }}" 
          # Add-Content $filePath "`nSLOT_HOST_NAME=${{ steps.create-slot.outputs.SLOT_HOST_NAME }}"
       # shell: pwsh
 
     # - name: Commit Azure File
      #  run: |
       #   git config --global user.name 'Kamil Hussain'  # move this to env
        #  git config --global user.email '<EMAIL>' # move this to env
         # git add -A
         # git commit -am "updated azure web app slot details to azurefile.txt."
         # git push
        # shell: gh
   #--------------------------------------------------end of reference code------------------------------
   
   # if the runner is self-hosted which is not github provided it is recommended to manually logout at the end of the workflow
      - name: Logout with Azure CLI script
        if: ${{ steps.login-azure.outcome == 'success' }} 
        uses: azure/CLI@v1
        with:
          inlineScript: |
           az logout
           az cache purge
           az account clear
